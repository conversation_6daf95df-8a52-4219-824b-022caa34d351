"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Phone, Mail, MapPin, Calendar, Clock, AlertTriangle } from "lucide-react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

interface LoanRecord {
  id: string
  customer_id: string
  customer_name: string
  customer_phone: string
  customer_email?: string
  customer_address?: string
  loan_amount: number
  amount_paid: number
  outstanding_amount: number
  due_date: string
  due_date_bs?: string
  current_stage: "early" | "assertive" | "escalation" | "legal_recovery" | "complete"
  stage_order: number
  days_overdue: number
  created_at: string
}

interface LoansByStage {
  early: LoanRecord[]
  assertive: LoanRecord[]
  escalation: LoanRecord[]
  legal_recovery: LoanRecord[]
  complete: LoanRecord[]
}

interface LoanRecoveryKanbanProps {
  loans: LoansByStage
  showCompleted: boolean
  onCustomerClick: (customerId: string) => void
  onRefresh: () => void
}

const stageConfig = {
  early: {
    title: "Early Stage",
    color: "bg-green-500",
    textColor: "text-green-700",
    bgColor: "bg-green-50 dark:bg-green-900/10",
    borderColor: "border-green-200 dark:border-green-800",
  },
  assertive: {
    title: "Assertive",
    color: "bg-orange-500",
    textColor: "text-orange-700",
    bgColor: "bg-orange-50 dark:bg-orange-900/10",
    borderColor: "border-orange-200 dark:border-orange-800",
  },
  escalation: {
    title: "Escalation",
    color: "bg-purple-500",
    textColor: "text-purple-700",
    bgColor: "bg-purple-50 dark:bg-purple-900/10",
    borderColor: "border-purple-200 dark:border-purple-800",
  },
  legal_recovery: {
    title: "Legal Recovery",
    color: "bg-red-500",
    textColor: "text-red-700",
    bgColor: "bg-red-50 dark:bg-red-900/10",
    borderColor: "border-red-200 dark:border-red-800",
  },
  complete: {
    title: "Complete",
    color: "bg-gray-500",
    textColor: "text-gray-700",
    bgColor: "bg-gray-50 dark:bg-gray-900/10",
    borderColor: "border-gray-200 dark:border-gray-800",
  },
}

// Update loan stage mutation
const updateLoanStage = async (loanId: string, newStage: string, newOrder?: number) => {
  const response = await fetch(`/api/loan-recovery/loans/${loanId}/stage`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify({
      new_stage: newStage,
      new_order: newOrder,
    }),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to update loan stage")
  }

  return response.json()
}

function LoanCard({
  loan,
  onCustomerClick,
  onDragStart,
  onDragEnd,
  isDragging
}: {
  loan: LoanRecord
  onCustomerClick: (customerId: string) => void
  onDragStart: (loan: LoanRecord) => void
  onDragEnd: () => void
  isDragging?: boolean
}) {
  const isOverdue = loan.days_overdue > 0
  const overdueText = loan.days_overdue > 0 ? `${loan.days_overdue} days overdue` : ""

  return (
    <Card
      className={`cursor-grab bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-all duration-200 ${
        isDragging ? 'opacity-50 scale-95 rotate-2' : ''
      }`}
      draggable
      onDragStart={() => onDragStart(loan)}
      onDragEnd={onDragEnd}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-exobank-green text-white text-xs">
                {loan.customer_name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <button
                onClick={() => {
                  console.log('🔍 Customer clicked:', { customerId: loan.customer_id, customerName: loan.customer_name })
                  onCustomerClick(loan.customer_id)
                }}
                className="font-medium text-sm hover:text-exobank-green transition-colors text-left"
              >
                {loan.customer_name}
              </button>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Phone className="h-3 w-3" />
                {loan.customer_phone}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-semibold text-green-600">
              Rs. {loan.outstanding_amount.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">
              Total: Rs. {loan.loan_amount.toLocaleString()}
            </div>
          </div>
        </div>

        {loan.customer_email && (
          <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
            <Mail className="h-3 w-3" />
            {loan.customer_email}
          </div>
        )}

        {loan.customer_address && (
          <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
            <MapPin className="h-3 w-3" />
            {loan.customer_address}
          </div>
        )}

        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1 text-gray-500">
            <Calendar className="h-3 w-3" />
            {loan.due_date_bs || loan.due_date}
          </div>
          {isOverdue && (
            <Badge variant="destructive" className="text-xs">
              <AlertTriangle className="h-3 w-3 mr-1" />
              {overdueText}
            </Badge>
          )}
        </div>


      </CardContent>
    </Card>
  )
}

function KanbanColumn({
  stage,
  loans,
  onCustomerClick,
  onDrop,
  onDragStart,
  onDragEnd,
  isDragging,
  dragOverStage,
  onDragOver,
  draggedLoan
}: {
  stage: keyof typeof stageConfig
  loans: LoanRecord[]
  onCustomerClick: (customerId: string) => void
  onDrop: (stage: string) => void
  onDragStart: (loan: LoanRecord) => void
  onDragEnd: () => void
  isDragging?: boolean
  dragOverStage?: string | null
  onDragOver?: (stage: string) => void
  draggedLoan?: LoanRecord | null
}) {
  const config = stageConfig[stage]
  const isDropTarget = isDragging && dragOverStage === stage

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (onDragOver) onDragOver(stage)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    onDrop(stage)
  }

  return (
    <div
      className={`${config.bgColor} ${config.borderColor} border-2 border-dashed rounded-lg p-4 min-h-[600px] transition-all duration-200 ${
        isDropTarget ? 'border-solid border-blue-500 bg-blue-50 scale-105' : ''
      } ${isDragging ? 'border-opacity-50' : ''}`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${config.color}`}></div>
          <h3 className={`font-semibold ${config.textColor}`}>{config.title}</h3>
        </div>
        <Badge variant="secondary" className="text-xs">
          {loans.length}
        </Badge>
      </div>

      <div className="space-y-3">
        {loans.map((loan) => (
          <LoanCard
            key={loan.id}
            loan={loan}
            onCustomerClick={onCustomerClick}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            isDragging={isDragging && draggedLoan?.id === loan.id}
          />
        ))}
      </div>
    </div>
  )
}

export function LoanRecoveryKanban({
  loans,
  showCompleted,
  onCustomerClick,
  onRefresh
}: LoanRecoveryKanbanProps) {
  const [draggedLoan, setDraggedLoan] = useState<LoanRecord | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragOverStage, setDragOverStage] = useState<string | null>(null)
  const queryClient = useQueryClient()

  const updateStageMutation = useMutation({
    mutationFn: ({ loanId, newStage }: { loanId: string; newStage: string }) =>
      updateLoanStage(loanId, newStage),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["loans"] })
      toast({
        title: "Success",
        description: "Loan stage updated successfully",
      })
      onRefresh()
      setIsDragging(false)
      setDragOverStage(null)
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
      setIsDragging(false)
      setDragOverStage(null)
    },
  })

  const handleDragStart = (loan: LoanRecord) => {
    if (updateStageMutation.isPending) return // Prevent drag during update
    setDraggedLoan(loan)
    setIsDragging(true)
  }

  const handleDragEnd = () => {
    setDraggedLoan(null)
    setIsDragging(false)
    setDragOverStage(null)
  }

  const handleDragOver = (stage: string) => {
    setDragOverStage(stage)
  }

  const handleDrop = (newStage: string) => {
    if (draggedLoan && draggedLoan.current_stage !== newStage && !updateStageMutation.isPending) {
      updateStageMutation.mutate({
        loanId: draggedLoan.id,
        newStage,
      })
    }
    setDragOverStage(null)
  }

  const visibleStages = showCompleted 
    ? (["early", "assertive", "escalation", "legal_recovery", "complete"] as const)
    : (["early", "assertive", "escalation", "legal_recovery"] as const)

  return (
    <div className={`grid gap-6 ${showCompleted ? 'grid-cols-1 lg:grid-cols-5' : 'grid-cols-1 lg:grid-cols-4'}`}>
      {visibleStages.map((stage) => (
        <KanbanColumn
          key={stage}
          stage={stage}
          loans={loans[stage] || []}
          onCustomerClick={onCustomerClick}
          onDrop={handleDrop}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          isDragging={isDragging}
          dragOverStage={dragOverStage}
          onDragOver={handleDragOver}
          draggedLoan={draggedLoan}
        />
      ))}
    </div>
  )
}
